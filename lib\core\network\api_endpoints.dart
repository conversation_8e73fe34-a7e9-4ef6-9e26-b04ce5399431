class EndUrl {
  // ==================== OMS (Order Management System) Endpoints ====================
  static const String loginUser = 'api/sign-in';
  static const String registerUser = 'api/register';
  static const String forgotPassword = 'api/forgot-password';

  static const String createOrder = 'app/v1/create_order';
  static const String getOrderHistory = 'app/v1/orders';
  static const String getOrderDetails = 'app/v1/order_details';
  static const String cancelOrder = 'api/orders'; // append /{orderId}/cancel
  static const String previousOrder = 'app/v1/order_again';
  static const String verifyPayment = 'app/v1/verify_payment';

  // ==================== IMS (Inventory Management System) Endpoints ====================
  static const String getStockAvailability = 'api/stock/multi';

  // ==================== Service Type Helpers ====================
  static bool isImsEndpoint(String endpoint) {
    const imsEndpoints = [
      getStockAvailability,
    ];

    return imsEndpoints.any((imsEndpoint) => endpoint.startsWith(imsEndpoint));
  }
}
